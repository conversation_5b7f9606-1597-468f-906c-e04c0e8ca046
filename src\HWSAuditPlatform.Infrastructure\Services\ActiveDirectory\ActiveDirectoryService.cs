using System.DirectoryServices;
using System.Runtime.Versioning;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory.Models;

namespace HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;

/// <summary>
/// Active Directory service implementation using System.DirectoryServices
/// </summary>
[SupportedOSPlatform("windows")]
public class ActiveDirectoryService : IActiveDirectoryService
{
    private readonly ActiveDirectoryOptions _options;
    private readonly ILogger<ActiveDirectoryService> _logger;

    public ActiveDirectoryService(IOptions<ActiveDirectoryOptions> options, ILogger<ActiveDirectoryService> logger)
    {
        ArgumentNullException.ThrowIfNull(options, nameof(options));
        ArgumentNullException.ThrowIfNull(logger, nameof(logger));

        _options = options.Value;
        _logger = logger;

        ValidateConfiguration();
    }

    private void ValidateConfiguration()
    {
        if (string.IsNullOrWhiteSpace(_options.Domain))
        {
            throw new ArgumentException("Domain is required and cannot be empty.", nameof(_options.Domain));
        }

        if (string.IsNullOrWhiteSpace(_options.Username))
        {
            throw new ArgumentException("Username is required and cannot be empty.", nameof(_options.Username));
        }

        if (string.IsNullOrWhiteSpace(_options.SearchBase))
        {
            throw new ArgumentException("SearchBase is required and cannot be empty.", nameof(_options.SearchBase));
        }
    }

    public async Task<IEnumerable<AdUser>> GetUsersAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            var users = new List<AdUser>();

            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = "(&(objectClass=user)(objectCategory=person))",
                    PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail", 
                                       "distinguishedName", "userAccountControl", "lastLogon", 
                                       "whenCreated", "whenChanged", "department", "title", "manager", 
                                       "telephoneNumber", "memberOf" }
                };

                var searchResults = directorySearcher.FindAll();

                foreach (SearchResult result in searchResults)
                {
                    var user = MapSearchResultToAdUser(result);
                    if (user != null)
                    {
                        users.Add(user);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users from Active Directory");
                throw;
            }

            return users;
        }, cancellationToken);
    }

    public async Task<AdUser?> GetUserByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(objectGuid, nameof(objectGuid));

        return await Task.Run(() =>
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = $"(&(objectClass=user)(objectGUID={objectGuid}))",
                    PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail",
                                       "distinguishedName", "userAccountControl", "lastLogon",
                                       "whenCreated", "whenChanged", "department", "title", "manager",
                                       "telephoneNumber", "memberOf" }
                };

                var searchResult = directorySearcher.FindOne();
                return searchResult != null ? MapSearchResultToAdUser(searchResult) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by ObjectGUID {ObjectGuid} from Active Directory", objectGuid);
                throw;
            }
        }, cancellationToken);
    }

    public async Task<AdUser?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username, nameof(username));

        return await Task.Run(() =>
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = $"(&(objectClass=user)(sAMAccountName={username}))",
                    PropertiesToLoad = { "objectGUID", "sAMAccountName", "givenName", "sn", "mail",
                                       "distinguishedName", "userAccountControl", "lastLogon",
                                       "whenCreated", "whenChanged", "department", "title", "manager",
                                       "telephoneNumber", "memberOf" }
                };

                var searchResult = directorySearcher.FindOne();
                return searchResult != null ? MapSearchResultToAdUser(searchResult) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user by username {Username} from Active Directory", username);
                throw;
            }
        }, cancellationToken);
    }

    public async Task<IEnumerable<AdGroup>> GetGroupsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            var groups = new List<AdGroup>();

            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = "(objectClass=group)",
                    PropertiesToLoad = { "objectGUID", "name", "distinguishedName", "description", 
                                       "groupType", "whenCreated", "whenChanged", "member" }
                };

                var searchResults = directorySearcher.FindAll();

                foreach (SearchResult result in searchResults)
                {
                    var group = MapSearchResultToAdGroup(result);
                    if (group != null)
                    {
                        groups.Add(group);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving groups from Active Directory");
                throw;
            }

            return groups;
        }, cancellationToken);
    }

    public async Task<AdGroup?> GetGroupByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(objectGuid, nameof(objectGuid));

        return await Task.Run(() =>
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                using var directorySearcher = new DirectorySearcher(directoryEntry)
                {
                    Filter = $"(&(objectClass=group)(objectGUID={objectGuid}))",
                    PropertiesToLoad = { "objectGUID", "name", "distinguishedName", "description",
                                       "groupType", "whenCreated", "whenChanged", "member" }
                };

                var searchResult = directorySearcher.FindOne();
                return searchResult != null ? MapSearchResultToAdGroup(searchResult) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving group by ObjectGUID {ObjectGuid} from Active Directory", objectGuid);
                throw;
            }
        }, cancellationToken);
    }

    public async Task<IEnumerable<AdUser>> GetGroupMembersAsync(string groupObjectGuid, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(groupObjectGuid, nameof(groupObjectGuid));

        // Implementation would query group members
        // This is a simplified version
        return await Task.FromResult(Enumerable.Empty<AdUser>());
    }

    public async Task<IEnumerable<AdGroup>> GetUserGroupsAsync(string userObjectGuid, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(userObjectGuid, nameof(userObjectGuid));

        // Implementation would query user's group memberships
        // This is a simplified version
        return await Task.FromResult(Enumerable.Empty<AdGroup>());
    }

    public async Task<bool> ValidateCredentialsAsync(string username, string password, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(username, nameof(username));
        ArgumentException.ThrowIfNullOrWhiteSpace(password, nameof(password));

        return await Task.Run(() =>
        {
            try
            {
                using var directoryEntry = new DirectoryEntry($"LDAP://{_options.Domain}", username, password);
                // Try to bind to the directory
                var nativeObject = directoryEntry.NativeObject;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to validate credentials for user {Username}", username);
                return false;
            }
        }, cancellationToken);
    }

    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        return await Task.Run(() =>
        {
            try
            {
                using var directoryEntry = CreateDirectoryEntry();
                var nativeObject = directoryEntry.NativeObject;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to connect to Active Directory");
                return false;
            }
        }, cancellationToken);
    }

    private DirectoryEntry CreateDirectoryEntry()
    {
        var ldapPath = $"LDAP://{_options.Domain}";
        if (!string.IsNullOrEmpty(_options.SearchBase))
        {
            ldapPath += $"/{_options.SearchBase}";
        }

        return new DirectoryEntry(ldapPath, _options.Username, _options.Password);
    }

    private static AdUser? MapSearchResultToAdUser(SearchResult result)
    {
        try
        {
            return new AdUser
            {
                ObjectGuid = GetPropertyValue(result, "objectGUID"),
                Username = GetPropertyValue(result, "sAMAccountName"),
                FirstName = GetPropertyValue(result, "givenName"),
                LastName = GetPropertyValue(result, "sn"),
                Email = GetPropertyValue(result, "mail"),
                DistinguishedName = GetPropertyValue(result, "distinguishedName"),
                IsActive = !IsAccountDisabled(result),
                Department = GetPropertyValue(result, "department"),
                Title = GetPropertyValue(result, "title"),
                Manager = GetPropertyValue(result, "manager"),
                Phone = GetPropertyValue(result, "telephoneNumber"),
                WhenCreated = GetDateTimeProperty(result, "whenCreated"),
                WhenChanged = GetDateTimeProperty(result, "whenChanged"),
                MemberOf = GetMultiValueProperty(result, "memberOf")
            };
        }
        catch (Exception)
        {
            return null;
        }
    }

    private static AdGroup? MapSearchResultToAdGroup(SearchResult result)
    {
        try
        {
            return new AdGroup
            {
                ObjectGuid = GetPropertyValue(result, "objectGUID"),
                Name = GetPropertyValue(result, "name"),
                DistinguishedName = GetPropertyValue(result, "distinguishedName"),
                Description = GetPropertyValue(result, "description"),
                GroupType = GetPropertyValue(result, "groupType"),
                WhenCreated = GetDateTimeProperty(result, "whenCreated"),
                WhenChanged = GetDateTimeProperty(result, "whenChanged"),
                Members = GetMultiValueProperty(result, "member")
            };
        }
        catch (Exception)
        {
            return null;
        }
    }

    private static string GetPropertyValue(SearchResult result, string propertyName)
    {
        return result.Properties[propertyName].Count > 0 ? result.Properties[propertyName][0]?.ToString() ?? string.Empty : string.Empty;
    }

    private static DateTime? GetDateTimeProperty(SearchResult result, string propertyName)
    {
        if (result.Properties[propertyName].Count > 0 && result.Properties[propertyName][0] is DateTime dateTime)
        {
            return dateTime;
        }
        return null;
    }

    private static List<string> GetMultiValueProperty(SearchResult result, string propertyName)
    {
        var values = new List<string>();
        for (int i = 0; i < result.Properties[propertyName].Count; i++)
        {
            var value = result.Properties[propertyName][i]?.ToString();
            if (!string.IsNullOrEmpty(value))
            {
                values.Add(value);
            }
        }
        return values;
    }

    private static bool IsAccountDisabled(SearchResult result)
    {
        if (result.Properties["userAccountControl"].Count > 0 && 
            int.TryParse(result.Properties["userAccountControl"][0]?.ToString(), out var userAccountControl))
        {
            // Check if the ACCOUNTDISABLE flag (0x2) is set
            return (userAccountControl & 0x2) != 0;
        }
        return false;
    }
}
