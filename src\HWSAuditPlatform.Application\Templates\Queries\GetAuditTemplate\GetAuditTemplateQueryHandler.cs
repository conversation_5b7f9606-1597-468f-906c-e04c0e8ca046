using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Templates.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Templates.Queries.GetAuditTemplate;

/// <summary>
/// Handler for GetAuditTemplateQuery
/// </summary>
public class GetAuditTemplateQueryHandler : BaseQueryHandler<GetAuditTemplateQuery, AuditTemplateDto>
{
    private readonly IApplicationDbContext _context;

    public GetAuditTemplateQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public override async Task<AuditTemplateDto> Handle(GetAuditTemplateQuery request, CancellationToken cancellationToken)
    {
        var template = await _context.AuditTemplates
            .Where(q => q.IsActive)
            .Include(t => t.QuestionGroups.Where(g => g.IsActive))
            .ThenInclude(g => g.Questions.Where(q => q.IsActive))
            .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .Include(t => t.Questions.Where(q => q.IsActive))
            .ThenInclude(q => q.Options.Where(o => o.IsActive))
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (template == null)
        {
            throw new NotFoundException(nameof(Domain.Entities.Templates.AuditTemplate), request.Id);
        }

        return new AuditTemplateDto
        {
            Id = template.Id,
            TemplateName = template.TemplateName,
            Description = template.Description,
            Version = template.Version,
            IsPublished = template.IsPublished,
            IsActive = template.IsActive,
            FullName = template.FullName,
            CanBeUsed = template.CanBeUsed,
            CreatedAt = template.CreatedAt,
            UpdatedAt = template.UpdatedAt,
            CreatedByUserId = template.CreatedByUserId,
            UpdatedByUserId = template.UpdatedByUserId,
            RecordVersion = template.RecordVersion,
            QuestionGroups = template.QuestionGroups
                .OrderBy(g => g.DisplayOrder)
                .Select(g => new QuestionGroupDto
                {
                    Id = g.Id,
                    AuditTemplateId = g.AuditTemplateId,
                    GroupName = g.GroupName,
                    Description = g.Description,
                    DisplayOrder = g.DisplayOrder,
                    IsActive = g.IsActive,
                    Questions = g.Questions
                        .Where(q => q.IsActive)
                        .OrderBy(q => q.DisplayOrder)
                        .Select(q => MapQuestionToDto(q))
                        .ToList()
                })
                .ToList(),
            Questions = template.Questions
                .Where(q => q.QuestionGroupId == null && q.IsActive) // Only root-level active questions
                .OrderBy(q => q.DisplayOrder)
                .Select(q => MapQuestionToDto(q))
                .ToList()
        };
    }

    private static QuestionDto MapQuestionToDto(Domain.Entities.Templates.Question question)
    {
        return new QuestionDto
        {
            Id = question.Id,
            AuditTemplateId = question.AuditTemplateId,
            QuestionGroupId = question.QuestionGroupId,
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType,
            DisplayOrder = question.DisplayOrder,
            IsRequired = question.IsRequired,
            Weight = question.Weight,
            HelpText = question.HelpText,
            ParentQuestionId = question.ParentQuestionId,
            TriggerAnswerValue = question.TriggerAnswerValue,
            SeverityLevel = question.SeverityLevel,
            EvidenceRequired = question.EvidenceRequired,
            AllowedEvidenceTypes = question.AllowedEvidenceTypes,
            IsActive = question.IsActive,
            IsConditional = question.IsConditional,
            Options = question.Options
                .OrderBy(o => o.DisplayOrder)
                .Select(o => new QuestionOptionDto
                {
                    Id = o.Id,
                    QuestionId = o.QuestionId,
                    OptionText = o.OptionText,
                    OptionValue = o.OptionValue,
                    DisplayOrder = o.DisplayOrder,
                    IsActive = o.IsActive
                })
                .ToList(),
            ChildQuestions = question.ChildQuestions
                .Where(cq => cq.IsActive)
                .OrderBy(cq => cq.DisplayOrder)
                .Select(cq => MapQuestionToDto(cq))
                .ToList()
        };
    }
}
