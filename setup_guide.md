I'll help you understand where to configure the database connection and Active Directory settings in your HWS Audit Platform. Let me first gather some information about the current configuration structure.

Augment Context Engine
Configuration files and settings for database connection strings and Active Directory configuration in the HWS Audit Platform. Look for appsettings.json, appsettings.Development.json, connection string configuration, AD/LDAP settings, domain configuration, and any authentication/authorization setup.
Perfect! Based on the codebase, here are simple instructions for setting up your database connection and Active Directory configuration:

Database Connection Setup
Location:  src\HWSAuditPlatform.ApiService\appsettings.json (for production) or appsettings.Development.json (for development)

src/HWSAuditPlatform.ApiService
"ConnectionStrings": {
  "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
}
Replace the connection string with your database details:

For SQL Server: "Server=YourServerName;Database=YourDatabaseName;User Id=YourUsername;Password=YourPassword;TrustServerCertificate=true"
For LocalDB (development): "Server=(localdb)\\mssqllocaldb;Database=YourDatabaseName;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"
Active Directory Configuration
Location: Same files - appsettings.json or appsettings.Development.json

src/HWSAuditPlatform.ApiService
Replace with your AD details:

Domain: Your AD domain name (e.g., "yourcompany.local" or "ad.yourcompany.com")
Username: Service account for AD queries (e.g., "<EMAIL>")
Password: Password for the service account
SearchBase: LDAP search base (e.g., "OU=Employees,DC=yourcompany,DC=local")
UseSSL: true for production (port 636), false for development (port 389)
Port: 636 for SSL, 389 for non-SSL
Quick Setup Summary:
Edit  src\HWSAuditPlatform.ApiService\appsettings.Development.json for development
Edit  src\HWSAuditPlatform.ApiService\appsettings.json for production
Update the ConnectionStrings.DefaultConnection with your database server details
Update the ActiveDirectory section with your domain and service account details
Run dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService to create the database
That's it! The application will automatically use these settings when it starts up.